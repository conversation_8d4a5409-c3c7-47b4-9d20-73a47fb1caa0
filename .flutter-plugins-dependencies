{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "app_links", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/app_links-6.4.0/", "native_build": true, "dependencies": []}, {"name": "path_provider_foundation", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "shared_preferences_foundation", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "url_launcher_ios", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/", "native_build": true, "dependencies": []}], "android": [{"name": "app_links", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/app_links-6.4.0/", "native_build": true, "dependencies": []}, {"name": "path_provider_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.10/", "native_build": true, "dependencies": []}, {"name": "shared_preferences_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.3.2/", "native_build": true, "dependencies": []}, {"name": "url_launcher_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.9/", "native_build": true, "dependencies": []}], "macos": [{"name": "app_links", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/app_links-6.4.0/", "native_build": true, "dependencies": []}, {"name": "path_provider_foundation", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "shared_preferences_foundation", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "url_launcher_macos", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/", "native_build": true, "dependencies": []}], "linux": [{"name": "app_links_linux", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/app_links_linux-1.0.3/", "native_build": false, "dependencies": ["gtk"]}, {"name": "gtk", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/gtk-2.1.0/", "native_build": true, "dependencies": []}, {"name": "path_provider_linux", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/", "native_build": false, "dependencies": []}, {"name": "shared_preferences_linux", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/", "native_build": false, "dependencies": ["path_provider_linux"]}, {"name": "url_launcher_linux", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/", "native_build": true, "dependencies": []}], "windows": [{"name": "app_links", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/app_links-6.4.0/", "native_build": true, "dependencies": []}, {"name": "path_provider_windows", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/", "native_build": false, "dependencies": []}, {"name": "shared_preferences_windows", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/", "native_build": false, "dependencies": ["path_provider_windows"]}, {"name": "url_launcher_windows", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/", "native_build": true, "dependencies": []}], "web": [{"name": "app_links_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/app_links_web-1.0.4/", "dependencies": []}, {"name": "shared_preferences_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/", "dependencies": []}, {"name": "url_launcher_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.3.3/", "dependencies": []}]}, "dependencyGraph": [{"name": "app_links", "dependencies": ["app_links_linux", "app_links_web"]}, {"name": "app_links_linux", "dependencies": ["gtk"]}, {"name": "app_links_web", "dependencies": []}, {"name": "gtk", "dependencies": []}, {"name": "path_provider", "dependencies": ["path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_windows"]}, {"name": "path_provider_android", "dependencies": []}, {"name": "path_provider_foundation", "dependencies": []}, {"name": "path_provider_linux", "dependencies": []}, {"name": "path_provider_windows", "dependencies": []}, {"name": "shared_preferences", "dependencies": ["shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "shared_preferences_android", "dependencies": []}, {"name": "shared_preferences_foundation", "dependencies": []}, {"name": "shared_preferences_linux", "dependencies": ["path_provider_linux"]}, {"name": "shared_preferences_web", "dependencies": []}, {"name": "shared_preferences_windows", "dependencies": ["path_provider_windows"]}, {"name": "url_launcher", "dependencies": ["url_launcher_android", "url_launcher_ios", "url_launcher_linux", "url_launcher_macos", "url_launcher_web", "url_launcher_windows"]}, {"name": "url_launcher_android", "dependencies": []}, {"name": "url_launcher_ios", "dependencies": []}, {"name": "url_launcher_linux", "dependencies": []}, {"name": "url_launcher_macos", "dependencies": []}, {"name": "url_launcher_web", "dependencies": []}, {"name": "url_launcher_windows", "dependencies": []}], "date_created": "2025-06-27 05:59:04.584600", "version": "3.22.1"}