import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'config/theme.dart';
import 'config/routes.dart';
import 'services/services.dart';
import 'models/models.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // تهيئة الخدمات
  await DatabaseService.initialize();
  await StorageService.instance.initialize();
  await AuthService.instance.initialize();

  runApp(const IkraeApp());
}

class IkraeApp extends StatelessWidget {
  const IkraeApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'إقرأ - التواصل التعليمي',
      debugShowCheckedModeBanner: false,
      theme: AppTheme.lightTheme,

      // إعدادات اللغة العربية
      locale: const Locale('ar', 'MA'), // العربية - المغرب
      supportedLocales: const [
        Locale('ar', 'MA'), // العربية - المغرب
        Locale('ar', ''), // العربية عامة
        Locale('en', ''), // الإنجليزية
        Locale('fr', ''), // الفرنسية
      ],
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],

      // نظام التوجيه
      routes: AppRoutes.routes,
      onUnknownRoute: AppRoutes.onUnknownRoute,

      // الصفحة الرئيسية
      home: const SplashScreen(),
    );
  }
}

/// شاشة البداية
class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    _initializeApp();
  }

  /// تهيئة التطبيق
  Future<void> _initializeApp() async {
    // انتظار لمدة ثانيتين لعرض شاشة البداية
    await Future.delayed(const Duration(seconds: 2));

    // التحقق من حالة تسجيل الدخول
    final authService = AuthService.instance;

    if (mounted) {
      if (authService.isLoggedIn) {
        // الانتقال إلى لوحة التحكم المناسبة حسب نوع المستخدم
        final user = authService.currentUser!;
        String route = AppRoutes.login; // قيمة افتراضية
        switch (user.userType) {
          case UserType.nationalAdmin:
            route = AppRoutes.nationalDashboard;
            break;
          case UserType.regionalDirector:
            route = AppRoutes.regionalDashboard;
            break;
          case UserType.provincialDirector:
            route = AppRoutes.provincialDashboard;
            break;
          case UserType.schoolPrincipal:
            route = AppRoutes.schoolDashboard;
            break;
          case UserType.teacher:
            route = AppRoutes.teacherDashboard;
            break;
          case UserType.student:
            route = AppRoutes.studentDashboard;
            break;
        }
        Navigator.of(context).pushReplacementNamed(route);
      } else {
        // الانتقال إلى صفحة تسجيل الدخول
        Navigator.of(context).pushReplacementNamed(AppRoutes.login);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.primaryColor,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // شعار التطبيق
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: const Icon(
                Icons.school,
                size: 60,
                color: AppTheme.primaryColor,
              ),
            ),

            const SizedBox(height: 30),

            // اسم التطبيق
            const Text(
              'إقرأ',
              style: TextStyle(
                fontSize: 36,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),

            const SizedBox(height: 10),

            // وصف التطبيق
            const Text(
              'التواصل التعليمي في المدارس المغربية',
              style: TextStyle(
                fontSize: 16,
                color: Colors.white70,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 50),

            // مؤشر التحميل
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
          ],
        ),
      ),
    );
  }
}
