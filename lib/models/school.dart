import 'user_type.dart';

/// نموذج المدرسة
class School {
  final String id;
  final String provinceId;
  final String nameAr;
  final String nameFr;
  final String address;
  final String? phone;
  final String? email;
  final SchoolType schoolType;
  final DateTime createdAt;

  const School({
    required this.id,
    required this.provinceId,
    required this.nameAr,
    required this.nameFr,
    required this.address,
    this.phone,
    this.email,
    required this.schoolType,
    required this.createdAt,
  });

  /// إنشاء مدرسة من JSON
  factory School.fromJson(Map<String, dynamic> json) {
    return School(
      id: json['id'] as String,
      provinceId: json['province_id'] as String,
      nameAr: json['name_ar'] as String,
      nameFr: json['name_fr'] as String,
      address: json['address'] as String,
      phone: json['phone'] as String?,
      email: json['email'] as String?,
      schoolType: SchoolType.fromString(json['school_type'] as String),
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  /// تحويل المدرسة إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'province_id': provinceId,
      'name_ar': nameAr,
      'name_fr': nameFr,
      'address': address,
      'phone': phone,
      'email': email,
      'school_type': schoolType.value,
      'created_at': createdAt.toIso8601String(),
    };
  }

  /// إنشاء نسخة محدثة من المدرسة
  School copyWith({
    String? id,
    String? provinceId,
    String? nameAr,
    String? nameFr,
    String? address,
    String? phone,
    String? email,
    SchoolType? schoolType,
    DateTime? createdAt,
  }) {
    return School(
      id: id ?? this.id,
      provinceId: provinceId ?? this.provinceId,
      nameAr: nameAr ?? this.nameAr,
      nameFr: nameFr ?? this.nameFr,
      address: address ?? this.address,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      schoolType: schoolType ?? this.schoolType,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is School && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'School(id: $id, nameAr: $nameAr, schoolType: ${schoolType.displayNameAr})';
  }
}

/// نموذج دور المستخدم في المؤسسة
class UserRole {
  final String id;
  final String userId;
  final String organizationId; // يمكن أن يكون معرف جهة، إقليم، أو مدرسة
  final String roleType; // نوع الدور (region, province, school)
  final DateTime assignedAt;
  final bool isActive;

  const UserRole({
    required this.id,
    required this.userId,
    required this.organizationId,
    required this.roleType,
    required this.assignedAt,
    this.isActive = true,
  });

  /// إنشاء دور من JSON
  factory UserRole.fromJson(Map<String, dynamic> json) {
    return UserRole(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      organizationId: json['organization_id'] as String,
      roleType: json['role_type'] as String,
      assignedAt: DateTime.parse(json['assigned_at'] as String),
      isActive: json['is_active'] as bool? ?? true,
    );
  }

  /// تحويل الدور إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'organization_id': organizationId,
      'role_type': roleType,
      'assigned_at': assignedAt.toIso8601String(),
      'is_active': isActive,
    };
  }

  /// إنشاء نسخة محدثة من الدور
  UserRole copyWith({
    String? id,
    String? userId,
    String? organizationId,
    String? roleType,
    DateTime? assignedAt,
    bool? isActive,
  }) {
    return UserRole(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      organizationId: organizationId ?? this.organizationId,
      roleType: roleType ?? this.roleType,
      assignedAt: assignedAt ?? this.assignedAt,
      isActive: isActive ?? this.isActive,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserRole && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'UserRole(id: $id, userId: $userId, roleType: $roleType)';
  }
}
