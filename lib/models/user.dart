import 'user_type.dart';

/// نموذج المستخدم
class User {
  final String id;
  final String email;
  final String fullName;
  final String? phone;
  final UserType userType;
  final String? profileImageUrl;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isActive;
  final String? parentId; // معرف المستخدم الذي يدير هذا المستخدم

  const User({
    required this.id,
    required this.email,
    required this.fullName,
    this.phone,
    required this.userType,
    this.profileImageUrl,
    required this.createdAt,
    required this.updatedAt,
    this.isActive = true,
    this.parentId,
  });

  /// إنشاء مستخدم من JSON
  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] as String,
      email: json['email'] as String,
      fullName: json['full_name'] as String,
      phone: json['phone'] as String?,
      userType: UserType.fromString(json['user_type'] as String),
      profileImageUrl: json['profile_image_url'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      isActive: json['is_active'] as bool? ?? true,
      parentId: json['parent_id'] as String?,
    );
  }

  /// تحويل المستخدم إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'full_name': fullName,
      'phone': phone,
      'user_type': userType.value,
      'profile_image_url': profileImageUrl,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'is_active': isActive,
      'parent_id': parentId,
    };
  }

  /// إنشاء نسخة محدثة من المستخدم
  User copyWith({
    String? id,
    String? email,
    String? fullName,
    String? phone,
    UserType? userType,
    String? profileImageUrl,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isActive,
    String? parentId,
  }) {
    return User(
      id: id ?? this.id,
      email: email ?? this.email,
      fullName: fullName ?? this.fullName,
      phone: phone ?? this.phone,
      userType: userType ?? this.userType,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isActive: isActive ?? this.isActive,
      parentId: parentId ?? this.parentId,
    );
  }

  /// التحقق من إمكانية إدارة مستخدم آخر
  bool canManage(User otherUser) {
    return userType.canManage(otherUser.userType);
  }

  /// التحقق من كون المستخدم إداري
  bool get isAdmin => userType.isAdmin;

  /// التحقق من كون المستخدم مستخدم نهائي
  bool get isEndUser => userType.isEndUser;

  /// الحصول على الاسم المختصر
  String get shortName {
    final parts = fullName.split(' ');
    if (parts.length >= 2) {
      return '${parts.first} ${parts.last}';
    }
    return fullName;
  }

  /// الحصول على الأحرف الأولى للاسم
  String get initials {
    final parts = fullName.split(' ');
    if (parts.length >= 2) {
      return '${parts.first[0]}${parts.last[0]}'.toUpperCase();
    } else if (parts.isNotEmpty) {
      return parts.first.substring(0, 1).toUpperCase();
    }
    return 'U';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is User && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'User(id: $id, email: $email, fullName: $fullName, userType: ${userType.displayNameAr})';
  }
}
