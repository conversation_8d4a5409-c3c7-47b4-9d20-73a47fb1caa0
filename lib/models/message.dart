import 'user_type.dart';

/// نموذج الرسالة
class Message {
  final String id;
  final String senderId;
  final String receiverId;
  final String subject;
  final String content;
  final MessageType messageType;
  final bool isRead;
  final DateTime sentAt;
  final DateTime? readAt;

  const Message({
    required this.id,
    required this.senderId,
    required this.receiverId,
    required this.subject,
    required this.content,
    required this.messageType,
    this.isRead = false,
    required this.sentAt,
    this.readAt,
  });

  /// إنشاء رسالة من JSON
  factory Message.fromJson(Map<String, dynamic> json) {
    return Message(
      id: json['id'] as String,
      senderId: json['sender_id'] as String,
      receiverId: json['receiver_id'] as String,
      subject: json['subject'] as String,
      content: json['content'] as String,
      messageType: MessageType.fromString(json['message_type'] as String),
      isRead: json['is_read'] as bool? ?? false,
      sentAt: DateTime.parse(json['sent_at'] as String),
      readAt: json['read_at'] != null 
          ? DateTime.parse(json['read_at'] as String) 
          : null,
    );
  }

  /// تحويل الرسالة إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'sender_id': senderId,
      'receiver_id': receiverId,
      'subject': subject,
      'content': content,
      'message_type': messageType.value,
      'is_read': isRead,
      'sent_at': sentAt.toIso8601String(),
      'read_at': readAt?.toIso8601String(),
    };
  }

  /// إنشاء نسخة محدثة من الرسالة
  Message copyWith({
    String? id,
    String? senderId,
    String? receiverId,
    String? subject,
    String? content,
    MessageType? messageType,
    bool? isRead,
    DateTime? sentAt,
    DateTime? readAt,
  }) {
    return Message(
      id: id ?? this.id,
      senderId: senderId ?? this.senderId,
      receiverId: receiverId ?? this.receiverId,
      subject: subject ?? this.subject,
      content: content ?? this.content,
      messageType: messageType ?? this.messageType,
      isRead: isRead ?? this.isRead,
      sentAt: sentAt ?? this.sentAt,
      readAt: readAt ?? this.readAt,
    );
  }

  /// تحديد الرسالة كمقروءة
  Message markAsRead() {
    return copyWith(
      isRead: true,
      readAt: DateTime.now(),
    );
  }

  /// التحقق من كون الرسالة عاجلة
  bool get isUrgent => messageType == MessageType.urgent;

  /// التحقق من كون الرسالة إعلان
  bool get isAnnouncement => messageType == MessageType.announcement;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Message && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Message(id: $id, subject: $subject, messageType: ${messageType.displayNameAr})';
  }
}

/// نموذج الإشعار
class AppNotification {
  final String id;
  final String userId;
  final String title;
  final String content;
  final NotificationType notificationType;
  final bool isRead;
  final DateTime createdAt;

  const AppNotification({
    required this.id,
    required this.userId,
    required this.title,
    required this.content,
    required this.notificationType,
    this.isRead = false,
    required this.createdAt,
  });

  /// إنشاء إشعار من JSON
  factory AppNotification.fromJson(Map<String, dynamic> json) {
    return AppNotification(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      title: json['title'] as String,
      content: json['content'] as String,
      notificationType: NotificationType.fromString(json['notification_type'] as String),
      isRead: json['is_read'] as bool? ?? false,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  /// تحويل الإشعار إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'title': title,
      'content': content,
      'notification_type': notificationType.value,
      'is_read': isRead,
      'created_at': createdAt.toIso8601String(),
    };
  }

  /// إنشاء نسخة محدثة من الإشعار
  AppNotification copyWith({
    String? id,
    String? userId,
    String? title,
    String? content,
    NotificationType? notificationType,
    bool? isRead,
    DateTime? createdAt,
  }) {
    return AppNotification(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      title: title ?? this.title,
      content: content ?? this.content,
      notificationType: notificationType ?? this.notificationType,
      isRead: isRead ?? this.isRead,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  /// تحديد الإشعار كمقروء
  AppNotification markAsRead() {
    return copyWith(isRead: true);
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AppNotification && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'AppNotification(id: $id, title: $title, notificationType: ${notificationType.displayNameAr})';
  }
}
