/// تعداد أنواع المستخدمين في النظام الهرمي
enum UserType {
  /// المسؤول الوطني - أعلى مستوى في النظام
  nationalAdmin('national_admin', 'المسؤول الوطني', 1),
  
  /// مدير الأكاديمية الجهوية
  regionalDirector('regional_director', 'مدير الأكاديمية الجهوية', 2),
  
  /// مدير المديرية الإقليمية
  provincialDirector('provincial_director', 'مدير المديرية الإقليمية', 3),
  
  /// مدير المدرسة
  schoolPrincipal('school_principal', 'مدير المدرسة', 4),
  
  /// أستاذ
  teacher('teacher', 'أستاذ', 5),
  
  /// تلميذ
  student('student', 'تلميذ', 6);

  const UserType(this.value, this.displayNameAr, this.hierarchyLevel);

  /// القيمة المخزنة في قاعدة البيانات
  final String value;
  
  /// الاسم المعروض باللغة العربية
  final String displayNameAr;
  
  /// مستوى الهرمية (1 = أعلى مستوى)
  final int hierarchyLevel;

  /// تحويل من String إلى UserType
  static UserType fromString(String value) {
    return UserType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => UserType.student,
    );
  }

  /// التحقق من إمكانية إدارة نوع مستخدم آخر
  bool canManage(UserType otherType) {
    return hierarchyLevel < otherType.hierarchyLevel;
  }

  /// الحصول على الأنواع التي يمكن لهذا النوع إدارتها
  List<UserType> getManagedTypes() {
    return UserType.values
        .where((type) => canManage(type))
        .toList();
  }

  /// التحقق من كون المستخدم إداري
  bool get isAdmin {
    return hierarchyLevel <= 4; // من المسؤول الوطني إلى مدير المدرسة
  }

  /// التحقق من كون المستخدم مستخدم نهائي
  bool get isEndUser {
    return hierarchyLevel > 4; // أستاذ أو تلميذ
  }
}

/// تعداد أنواع المدارس
enum SchoolType {
  primary('primary', 'ابتدائي'),
  middle('middle', 'إعدادي'),
  secondary('secondary', 'ثانوي'),
  mixed('mixed', 'مختلط');

  const SchoolType(this.value, this.displayNameAr);

  final String value;
  final String displayNameAr;

  static SchoolType fromString(String value) {
    return SchoolType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => SchoolType.primary,
    );
  }
}

/// تعداد أنواع الرسائل
enum MessageType {
  announcement('announcement', 'إعلان'),
  notification('notification', 'إشعار'),
  personal('personal', 'رسالة شخصية'),
  urgent('urgent', 'عاجل');

  const MessageType(this.value, this.displayNameAr);

  final String value;
  final String displayNameAr;

  static MessageType fromString(String value) {
    return MessageType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => MessageType.personal,
    );
  }
}

/// تعداد أنواع الإشعارات
enum NotificationType {
  newMessage('new_message', 'رسالة جديدة'),
  userRegistration('user_registration', 'تسجيل مستخدم جديد'),
  systemUpdate('system_update', 'تحديث النظام'),
  warning('warning', 'تحذير'),
  info('info', 'معلومات');

  const NotificationType(this.value, this.displayNameAr);

  final String value;
  final String displayNameAr;

  static NotificationType fromString(String value) {
    return NotificationType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => NotificationType.info,
    );
  }
}
