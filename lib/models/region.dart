/// نموذج الجهة/المنطقة
class Region {
  final String id;
  final String nameAr;
  final String nameFr;
  final String code;
  final DateTime createdAt;

  const Region({
    required this.id,
    required this.nameAr,
    required this.nameFr,
    required this.code,
    required this.createdAt,
  });

  /// إنشاء جهة من JSON
  factory Region.fromJson(Map<String, dynamic> json) {
    return Region(
      id: json['id'] as String,
      nameAr: json['name_ar'] as String,
      nameFr: json['name_fr'] as String,
      code: json['code'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  /// تحويل الجهة إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name_ar': nameAr,
      'name_fr': nameFr,
      'code': code,
      'created_at': createdAt.toIso8601String(),
    };
  }

  /// إنشاء نسخة محدثة من الجهة
  Region copyWith({
    String? id,
    String? nameAr,
    String? nameFr,
    String? code,
    DateTime? createdAt,
  }) {
    return Region(
      id: id ?? this.id,
      nameAr: nameAr ?? this.nameAr,
      nameFr: nameFr ?? this.nameFr,
      code: code ?? this.code,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Region && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Region(id: $id, nameAr: $nameAr, code: $code)';
  }
}

/// نموذج الإقليم/المحافظة
class Province {
  final String id;
  final String regionId;
  final String nameAr;
  final String nameFr;
  final String code;
  final DateTime createdAt;

  const Province({
    required this.id,
    required this.regionId,
    required this.nameAr,
    required this.nameFr,
    required this.code,
    required this.createdAt,
  });

  /// إنشاء إقليم من JSON
  factory Province.fromJson(Map<String, dynamic> json) {
    return Province(
      id: json['id'] as String,
      regionId: json['region_id'] as String,
      nameAr: json['name_ar'] as String,
      nameFr: json['name_fr'] as String,
      code: json['code'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  /// تحويل الإقليم إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'region_id': regionId,
      'name_ar': nameAr,
      'name_fr': nameFr,
      'code': code,
      'created_at': createdAt.toIso8601String(),
    };
  }

  /// إنشاء نسخة محدثة من الإقليم
  Province copyWith({
    String? id,
    String? regionId,
    String? nameAr,
    String? nameFr,
    String? code,
    DateTime? createdAt,
  }) {
    return Province(
      id: id ?? this.id,
      regionId: regionId ?? this.regionId,
      nameAr: nameAr ?? this.nameAr,
      nameFr: nameFr ?? this.nameFr,
      code: code ?? this.code,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Province && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Province(id: $id, nameAr: $nameAr, code: $code)';
  }
}
