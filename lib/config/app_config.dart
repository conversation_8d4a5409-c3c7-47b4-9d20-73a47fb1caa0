/// إعدادات التطبيق
class AppConfig {
  // معلومات التطبيق
  static const String appName = 'إقرأ';
  static const String appNameEn = 'ikrae';
  static const String appVersion = '1.0.0';

  // إعدادات Supabase - يتم استيرادها من ملف منفصل
  // راجع supabase_config.dart لتحديث هذه القيم

  // إعدادات قاعدة البيانات
  static const String usersTable = 'users';
  static const String regionsTable = 'regions';
  static const String provincesTable = 'provinces';
  static const String schoolsTable = 'schools';
  static const String userRolesTable = 'user_roles';
  static const String messagesTable = 'messages';
  static const String notificationsTable = 'notifications';

  // إعدادات التطبيق
  static const int maxMessageLength = 1000;
  static const int maxSubjectLength = 100;
  static const int sessionTimeoutMinutes = 30;

  // إعدادات الواجهة
  static const String defaultLocale = 'ar';
  static const String fallbackLocale = 'en';

  // ألوان التطبيق
  static const int primaryColorValue = 0xFF2E7D32; // أخضر داكن
  static const int secondaryColorValue = 0xFF4CAF50; // أخضر فاتح
  static const int accentColorValue = 0xFFFF9800; // برتقالي
  static const int errorColorValue = 0xFFD32F2F; // أحمر

  // أحجام الخط
  static const double fontSizeSmall = 12.0;
  static const double fontSizeMedium = 16.0;
  static const double fontSizeLarge = 20.0;
  static const double fontSizeXLarge = 24.0;

  // المسافات
  static const double paddingSmall = 8.0;
  static const double paddingMedium = 16.0;
  static const double paddingLarge = 24.0;
  static const double paddingXLarge = 32.0;

  // أبعاد العناصر
  static const double buttonHeight = 48.0;
  static const double inputHeight = 56.0;
  static const double cardElevation = 4.0;
  static const double borderRadius = 8.0;

  // إعدادات الشبكة
  static const int connectionTimeoutSeconds = 30;
  static const int receiveTimeoutSeconds = 30;

  // مسارات الصور
  static const String defaultAvatarPath = 'assets/images/default_avatar.png';
  static const String logoPath = 'assets/images/logo.png';
  static const String splashImagePath = 'assets/images/splash.png';

  // مفاتيح التخزين المحلي
  static const String userTokenKey = 'user_token';
  static const String userDataKey = 'user_data';
  static const String languageKey = 'language';
  static const String themeKey = 'theme';

  // رسائل الخطأ
  static const String networkErrorMessage = 'خطأ في الاتصال بالشبكة';
  static const String serverErrorMessage = 'خطأ في الخادم';
  static const String unauthorizedErrorMessage = 'غير مخول للوصول';
  static const String notFoundErrorMessage = 'البيانات غير موجودة';
  static const String validationErrorMessage = 'بيانات غير صحيحة';

  // رسائل النجاح
  static const String loginSuccessMessage = 'تم تسجيل الدخول بنجاح';
  static const String logoutSuccessMessage = 'تم تسجيل الخروج بنجاح';
  static const String updateSuccessMessage = 'تم التحديث بنجاح';
  static const String deleteSuccessMessage = 'تم الحذف بنجاح';
  static const String createSuccessMessage = 'تم الإنشاء بنجاح';
}
