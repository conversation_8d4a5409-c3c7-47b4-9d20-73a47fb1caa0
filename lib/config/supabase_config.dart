/// إعدادات Supabase
/// 
/// ملاحظة: يجب تحديث هذه القيم بقيم Supabase الحقيقية
/// عند النشر في الإنتاج
class SupabaseConfig {
  // URL مشروع Supabase
  static const String supabaseUrl = 'https://your-project.supabase.co';
  
  // مفتاح Supabase العام (anon key)
  static const String supabaseAnonKey = 'your-anon-key-here';
  
  // مفتاح Supabase الخدمي (service key) - للعمليات الإدارية فقط
  static const String supabaseServiceKey = 'your-service-key-here';
  
  /// التحقق من صحة التكوين
  static bool get isConfigured {
    return supabaseUrl != 'https://your-project.supabase.co' &&
           supabaseAnonKey != 'your-anon-key-here';
  }
  
  /// رسالة خطأ التكوين
  static String get configurationError {
    return 'يجب تكوين Supabase أولاً في ملف supabase_config.dart';
  }
}

/// سكريبت SQL لإنشاء الجداول في Supabase
/// 
/// يجب تشغيل هذا السكريبت في SQL Editor في لوحة تحكم Supabase
const String createTablesSQL = '''
-- إنشاء جدول الجهات
CREATE TABLE IF NOT EXISTS regions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name_ar TEXT NOT NULL,
    name_fr TEXT NOT NULL,
    code TEXT UNIQUE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول الأقاليم
CREATE TABLE IF NOT EXISTS provinces (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    region_id UUID REFERENCES regions(id) ON DELETE CASCADE,
    name_ar TEXT NOT NULL,
    name_fr TEXT NOT NULL,
    code TEXT UNIQUE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول المدارس
CREATE TABLE IF NOT EXISTS schools (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    province_id UUID REFERENCES provinces(id) ON DELETE CASCADE,
    name_ar TEXT NOT NULL,
    name_fr TEXT NOT NULL,
    address TEXT NOT NULL,
    phone TEXT,
    email TEXT,
    school_type TEXT NOT NULL CHECK (school_type IN ('primary', 'middle', 'secondary', 'mixed')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول المستخدمين
CREATE TABLE IF NOT EXISTS users (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    full_name TEXT NOT NULL,
    phone TEXT,
    user_type TEXT NOT NULL CHECK (user_type IN ('national_admin', 'regional_director', 'provincial_director', 'school_principal', 'teacher', 'student')),
    profile_image_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE,
    parent_id UUID REFERENCES users(id) ON DELETE SET NULL
);

-- إنشاء جدول أدوار المستخدمين
CREATE TABLE IF NOT EXISTS user_roles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    organization_id UUID NOT NULL, -- يمكن أن يكون معرف جهة، إقليم، أو مدرسة
    role_type TEXT NOT NULL CHECK (role_type IN ('region', 'province', 'school')),
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE
);

-- إنشاء جدول الرسائل
CREATE TABLE IF NOT EXISTS messages (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    sender_id UUID REFERENCES users(id) ON DELETE CASCADE,
    receiver_id UUID REFERENCES users(id) ON DELETE CASCADE,
    subject TEXT NOT NULL,
    content TEXT NOT NULL,
    message_type TEXT NOT NULL CHECK (message_type IN ('announcement', 'notification', 'personal', 'urgent')),
    is_read BOOLEAN DEFAULT FALSE,
    sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    read_at TIMESTAMP WITH TIME ZONE
);

-- إنشاء جدول الإشعارات
CREATE TABLE IF NOT EXISTS notifications (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    notification_type TEXT NOT NULL CHECK (notification_type IN ('new_message', 'user_registration', 'system_update', 'warning', 'info')),
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_provinces_region_id ON provinces(region_id);
CREATE INDEX IF NOT EXISTS idx_schools_province_id ON schools(province_id);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_user_type ON users(user_type);
CREATE INDEX IF NOT EXISTS idx_users_parent_id ON users(parent_id);
CREATE INDEX IF NOT EXISTS idx_user_roles_user_id ON user_roles(user_id);
CREATE INDEX IF NOT EXISTS idx_user_roles_organization_id ON user_roles(organization_id);
CREATE INDEX IF NOT EXISTS idx_messages_sender_id ON messages(sender_id);
CREATE INDEX IF NOT EXISTS idx_messages_receiver_id ON messages(receiver_id);
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);

-- إنشاء دالة لتحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS \$\$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
\$\$ language 'plpgsql';

-- إنشاء trigger لتحديث updated_at في جدول المستخدمين
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- إدراج بيانات تجريبية (اختيارية)
-- المسؤول الوطني الافتراضي
INSERT INTO users (email, password_hash, full_name, user_type) 
VALUES ('<EMAIL>', 'e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855', 'المسؤول الوطني', 'national_admin')
ON CONFLICT (email) DO NOTHING;

-- بعض الجهات التجريبية
INSERT INTO regions (name_ar, name_fr, code) VALUES 
('الرباط سلا القنيطرة', 'Rabat-Salé-Kénitra', 'RSK'),
('الدار البيضاء سطات', 'Casablanca-Settat', 'CS'),
('فاس مكناس', 'Fès-Meknès', 'FM')
ON CONFLICT (code) DO NOTHING;
''';
