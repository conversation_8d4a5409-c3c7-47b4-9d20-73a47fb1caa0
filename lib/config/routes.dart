import 'package:flutter/material.dart';
import '../screens/auth/login_screen.dart';
import '../screens/dashboards/national_dashboard.dart';

/// إعدادات التوجيه في التطبيق
class AppRoutes {
  // أسماء المسارات
  static const String splash = '/';
  static const String login = '/login';
  static const String nationalDashboard = '/national-dashboard';
  static const String regionalDashboard = '/regional-dashboard';
  static const String provincialDashboard = '/provincial-dashboard';
  static const String schoolDashboard = '/school-dashboard';
  static const String teacherDashboard = '/teacher-dashboard';
  static const String studentDashboard = '/student-dashboard';
  
  /// خريطة المسارات
  static Map<String, WidgetBuilder> get routes {
    return {
      login: (context) => const LoginScreen(),
      nationalDashboard: (context) => const NationalDashboard(),
      // TODO: إضافة باقي لوحات التحكم
      regionalDashboard: (context) => const PlaceholderScreen(title: 'لوحة تحكم مدير الأكاديمية الجهوية'),
      provincialDashboard: (context) => const PlaceholderScreen(title: 'لوحة تحكم مدير المديرية الإقليمية'),
      schoolDashboard: (context) => const PlaceholderScreen(title: 'لوحة تحكم مدير المدرسة'),
      teacherDashboard: (context) => const PlaceholderScreen(title: 'لوحة تحكم الأستاذ'),
      studentDashboard: (context) => const PlaceholderScreen(title: 'لوحة تحكم التلميذ'),
    };
  }
  
  /// معالج المسارات غير المعروفة
  static Route<dynamic> onUnknownRoute(RouteSettings settings) {
    return MaterialPageRoute(
      builder: (context) => const NotFoundScreen(),
    );
  }
}

/// شاشة مؤقتة للصفحات غير المكتملة
class PlaceholderScreen extends StatelessWidget {
  final String title;
  
  const PlaceholderScreen({
    super.key,
    required this.title,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(title),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.construction,
              size: 80,
              color: Colors.grey,
            ),
            const SizedBox(height: 20),
            Text(
              title,
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 10),
            const Text(
              'هذه الصفحة قيد التطوير',
              style: TextStyle(
                color: Colors.grey,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 30),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('العودة'),
            ),
          ],
        ),
      ),
    );
  }
}

/// شاشة الصفحة غير موجودة
class NotFoundScreen extends StatelessWidget {
  const NotFoundScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('صفحة غير موجودة'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 80,
              color: Colors.red,
            ),
            const SizedBox(height: 20),
            Text(
              '404',
              style: Theme.of(context).textTheme.displayLarge?.copyWith(
                color: Colors.red,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 10),
            const Text(
              'الصفحة المطلوبة غير موجودة',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 30),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pushNamedAndRemoveUntil(
                  AppRoutes.login,
                  (route) => false,
                );
              },
              child: const Text('العودة إلى الصفحة الرئيسية'),
            ),
          ],
        ),
      ),
    );
  }
}
