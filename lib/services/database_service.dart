import '../models/models.dart' as models;

/// خدمة قاعدة البيانات المبسطة للتطوير
class DatabaseService {
  static DatabaseService? _instance;
  static DatabaseService get instance => _instance ??= DatabaseService._();
  
  DatabaseService._();
  
  // بيانات وهمية للتطوير
  static final List<models.User> _users = [];
  static final List<models.Region> _regions = [];
  static final List<models.Province> _provinces = [];
  static final List<models.School> _schools = [];
  static final List<models.Message> _messages = [];
  static final List<models.AppNotification> _notifications = [];
  
  /// تهيئة قاعدة البيانات
  static Future<void> initialize() async {
    // إنشاء بيانات وهمية للتطوير
    _initializeDummyData();
    print('تم تهيئة قاعدة البيانات الوهمية للتطوير');
  }
  
  /// إنشاء بيانات وهمية
  static void _initializeDummyData() {
    // إنشاء المسؤول الوطني
    _users.add(models.User(
      id: '1',
      email: '<EMAIL>',
      fullName: 'المسؤول الوطني',
      userType: models.UserType.nationalAdmin,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    ));
    
    // إنشاء بعض الجهات
    _regions.addAll([
      models.Region(
        id: '1',
        nameAr: 'الرباط سلا القنيطرة',
        nameFr: 'Rabat-Salé-Kénitra',
        code: 'RSK',
        createdAt: DateTime.now(),
      ),
      models.Region(
        id: '2',
        nameAr: 'الدار البيضاء سطات',
        nameFr: 'Casablanca-Settat',
        code: 'CS',
        createdAt: DateTime.now(),
      ),
    ]);
  }
  
  // ==================== خدمات المستخدمين ====================
  
  /// الحصول على مستخدم بالمعرف
  Future<models.User?> getUserById(String userId) async {
    try {
      return _users.firstWhere((user) => user.id == userId);
    } catch (e) {
      return null;
    }
  }
  
  /// الحصول على مستخدم بالبريد الإلكتروني
  Future<models.User?> getUserByEmail(String email) async {
    try {
      return _users.firstWhere((user) => user.email == email);
    } catch (e) {
      return null;
    }
  }
  
  /// إنشاء مستخدم جديد
  Future<models.User?> createUser(models.User user) async {
    try {
      _users.add(user);
      return user;
    } catch (e) {
      print('خطأ في إنشاء المستخدم: $e');
      return null;
    }
  }
  
  /// تحديث مستخدم
  Future<models.User?> updateUser(models.User user) async {
    try {
      final index = _users.indexWhere((u) => u.id == user.id);
      if (index != -1) {
        _users[index] = user;
        return user;
      }
      return null;
    } catch (e) {
      print('خطأ في تحديث المستخدم: $e');
      return null;
    }
  }
  
  /// حذف مستخدم
  Future<bool> deleteUser(String userId) async {
    try {
      _users.removeWhere((user) => user.id == userId);
      return true;
    } catch (e) {
      print('خطأ في حذف المستخدم: $e');
      return false;
    }
  }
  
  /// الحصول على المستخدمين حسب النوع
  Future<List<models.User>> getUsersByType(models.UserType userType) async {
    try {
      return _users.where((user) => user.userType == userType && user.isActive).toList();
    } catch (e) {
      print('خطأ في الحصول على المستخدمين حسب النوع: $e');
      return [];
    }
  }
  
  /// الحصول على المستخدمين التابعين لمستخدم معين
  Future<List<models.User>> getSubordinateUsers(String parentId) async {
    try {
      return _users.where((user) => user.parentId == parentId && user.isActive).toList();
    } catch (e) {
      print('خطأ في الحصول على المستخدمين التابعين: $e');
      return [];
    }
  }
  
  // ==================== خدمات الجهات والأقاليم ====================
  
  /// الحصول على جميع الجهات
  Future<List<models.Region>> getAllRegions() async {
    try {
      return List.from(_regions);
    } catch (e) {
      print('خطأ في الحصول على الجهات: $e');
      return [];
    }
  }
  
  /// الحصول على الأقاليم حسب الجهة
  Future<List<models.Province>> getProvincesByRegion(String regionId) async {
    try {
      return _provinces.where((province) => province.regionId == regionId).toList();
    } catch (e) {
      print('خطأ في الحصول على الأقاليم: $e');
      return [];
    }
  }
  
  /// الحصول على المدارس حسب الإقليم
  Future<List<models.School>> getSchoolsByProvince(String provinceId) async {
    try {
      return _schools.where((school) => school.provinceId == provinceId).toList();
    } catch (e) {
      print('خطأ في الحصول على المدارس: $e');
      return [];
    }
  }
  
  // ==================== خدمات الرسائل ====================
  
  /// إرسال رسالة
  Future<models.Message?> sendMessage(models.Message message) async {
    try {
      _messages.add(message);
      return message;
    } catch (e) {
      print('خطأ في إرسال الرسالة: $e');
      return null;
    }
  }
  
  /// الحصول على رسائل المستخدم
  Future<List<models.Message>> getUserMessages(String userId) async {
    try {
      return _messages.where((message) => message.receiverId == userId).toList()
        ..sort((a, b) => b.sentAt.compareTo(a.sentAt));
    } catch (e) {
      print('خطأ في الحصول على الرسائل: $e');
      return [];
    }
  }
  
  /// تحديد رسالة كمقروءة
  Future<bool> markMessageAsRead(String messageId) async {
    try {
      final index = _messages.indexWhere((message) => message.id == messageId);
      if (index != -1) {
        _messages[index] = _messages[index].markAsRead();
        return true;
      }
      return false;
    } catch (e) {
      print('خطأ في تحديد الرسالة كمقروءة: $e');
      return false;
    }
  }
  
  // ==================== خدمات الإشعارات ====================
  
  /// إنشاء إشعار
  Future<models.AppNotification?> createNotification(models.AppNotification notification) async {
    try {
      _notifications.add(notification);
      return notification;
    } catch (e) {
      print('خطأ في إنشاء الإشعار: $e');
      return null;
    }
  }
  
  /// الحصول على إشعارات المستخدم
  Future<List<models.AppNotification>> getUserNotifications(String userId) async {
    try {
      return _notifications.where((notification) => notification.userId == userId).toList()
        ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
    } catch (e) {
      print('خطأ في الحصول على الإشعارات: $e');
      return [];
    }
  }
}
