import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../config/app_config.dart';
import '../models/models.dart';

/// خدمة التخزين المحلي
class StorageService {
  static StorageService? _instance;
  static StorageService get instance => _instance ??= StorageService._();
  
  StorageService._();
  
  SharedPreferences? _prefs;
  
  /// تهيئة خدمة التخزين
  Future<void> initialize() async {
    _prefs ??= await SharedPreferences.getInstance();
  }
  
  /// التأكد من تهيئة SharedPreferences
  Future<SharedPreferences> get _preferences async {
    _prefs ??= await SharedPreferences.getInstance();
    return _prefs!;
  }
  
  // ==================== إدارة بيانات المستخدم ====================
  
  /// حفظ بيانات المستخدم
  Future<bool> saveUserData(User user) async {
    try {
      final prefs = await _preferences;
      final userJson = jsonEncode(user.toJson());
      return await prefs.setString(AppConfig.userDataKey, userJson);
    } catch (e) {
      print('خطأ في حفظ بيانات المستخدم: $e');
      return false;
    }
  }
  
  /// الحصول على بيانات المستخدم
  Future<User?> getUserData() async {
    try {
      final prefs = await _preferences;
      final userJson = prefs.getString(AppConfig.userDataKey);
      if (userJson != null) {
        final userMap = jsonDecode(userJson) as Map<String, dynamic>;
        return User.fromJson(userMap);
      }
      return null;
    } catch (e) {
      print('خطأ في الحصول على بيانات المستخدم: $e');
      return null;
    }
  }
  
  /// مسح بيانات المستخدم
  Future<bool> clearUserData() async {
    try {
      final prefs = await _preferences;
      return await prefs.remove(AppConfig.userDataKey);
    } catch (e) {
      print('خطأ في مسح بيانات المستخدم: $e');
      return false;
    }
  }
  
  // ==================== إدارة الرمز المميز ====================
  
  /// حفظ الرمز المميز
  Future<bool> saveUserToken(String token) async {
    try {
      final prefs = await _preferences;
      return await prefs.setString(AppConfig.userTokenKey, token);
    } catch (e) {
      print('خطأ في حفظ الرمز المميز: $e');
      return false;
    }
  }
  
  /// الحصول على الرمز المميز
  Future<String?> getUserToken() async {
    try {
      final prefs = await _preferences;
      return prefs.getString(AppConfig.userTokenKey);
    } catch (e) {
      print('خطأ في الحصول على الرمز المميز: $e');
      return null;
    }
  }
  
  /// مسح الرمز المميز
  Future<bool> clearUserToken() async {
    try {
      final prefs = await _preferences;
      return await prefs.remove(AppConfig.userTokenKey);
    } catch (e) {
      print('خطأ في مسح الرمز المميز: $e');
      return false;
    }
  }
  
  // ==================== إعدادات التطبيق ====================
  
  /// حفظ اللغة المختارة
  Future<bool> saveLanguage(String languageCode) async {
    try {
      final prefs = await _preferences;
      return await prefs.setString(AppConfig.languageKey, languageCode);
    } catch (e) {
      print('خطأ في حفظ اللغة: $e');
      return false;
    }
  }
  
  /// الحصول على اللغة المختارة
  Future<String> getLanguage() async {
    try {
      final prefs = await _preferences;
      return prefs.getString(AppConfig.languageKey) ?? AppConfig.defaultLocale;
    } catch (e) {
      print('خطأ في الحصول على اللغة: $e');
      return AppConfig.defaultLocale;
    }
  }
  
  /// حفظ نمط الثيم
  Future<bool> saveTheme(String theme) async {
    try {
      final prefs = await _preferences;
      return await prefs.setString(AppConfig.themeKey, theme);
    } catch (e) {
      print('خطأ في حفظ الثيم: $e');
      return false;
    }
  }
  
  /// الحصول على نمط الثيم
  Future<String> getTheme() async {
    try {
      final prefs = await _preferences;
      return prefs.getString(AppConfig.themeKey) ?? 'light';
    } catch (e) {
      print('خطأ في الحصول على الثيم: $e');
      return 'light';
    }
  }
  
  // ==================== إعدادات عامة ====================
  
  /// حفظ قيمة منطقية
  Future<bool> saveBool(String key, bool value) async {
    try {
      final prefs = await _preferences;
      return await prefs.setBool(key, value);
    } catch (e) {
      print('خطأ في حفظ القيمة المنطقية: $e');
      return false;
    }
  }
  
  /// الحصول على قيمة منطقية
  Future<bool> getBool(String key, {bool defaultValue = false}) async {
    try {
      final prefs = await _preferences;
      return prefs.getBool(key) ?? defaultValue;
    } catch (e) {
      print('خطأ في الحصول على القيمة المنطقية: $e');
      return defaultValue;
    }
  }
  
  /// حفظ قيمة نصية
  Future<bool> saveString(String key, String value) async {
    try {
      final prefs = await _preferences;
      return await prefs.setString(key, value);
    } catch (e) {
      print('خطأ في حفظ القيمة النصية: $e');
      return false;
    }
  }
  
  /// الحصول على قيمة نصية
  Future<String?> getString(String key) async {
    try {
      final prefs = await _preferences;
      return prefs.getString(key);
    } catch (e) {
      print('خطأ في الحصول على القيمة النصية: $e');
      return null;
    }
  }
  
  /// حفظ قيمة رقمية
  Future<bool> saveInt(String key, int value) async {
    try {
      final prefs = await _preferences;
      return await prefs.setInt(key, value);
    } catch (e) {
      print('خطأ في حفظ القيمة الرقمية: $e');
      return false;
    }
  }
  
  /// الحصول على قيمة رقمية
  Future<int> getInt(String key, {int defaultValue = 0}) async {
    try {
      final prefs = await _preferences;
      return prefs.getInt(key) ?? defaultValue;
    } catch (e) {
      print('خطأ في الحصول على القيمة الرقمية: $e');
      return defaultValue;
    }
  }
  
  /// مسح قيمة معينة
  Future<bool> remove(String key) async {
    try {
      final prefs = await _preferences;
      return await prefs.remove(key);
    } catch (e) {
      print('خطأ في مسح القيمة: $e');
      return false;
    }
  }
  
  /// مسح جميع البيانات
  Future<bool> clearAll() async {
    try {
      final prefs = await _preferences;
      return await prefs.clear();
    } catch (e) {
      print('خطأ في مسح جميع البيانات: $e');
      return false;
    }
  }
}
