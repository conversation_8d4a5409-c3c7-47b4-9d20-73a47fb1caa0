import 'package:crypto/crypto.dart';
import 'dart:convert';
import '../models/models.dart' as models;
import 'database_service.dart';
import 'storage_service.dart';

/// خدمة المصادقة والتفويض المبسطة للتطوير
class AuthService {
  static AuthService? _instance;
  static AuthService get instance => _instance ??= AuthService._();

  AuthService._();

  /// خدمة قاعدة البيانات
  final DatabaseService _databaseService = DatabaseService.instance;

  /// خدمة التخزين المحلي
  final StorageService _storageService = StorageService.instance;

  /// المستخدم الحالي
  models.User? _currentUser;
  models.User? get currentUser => _currentUser;

  /// التحقق من حالة تسجيل الدخول
  bool get isLoggedIn => _currentUser != null;

  /// تهيئة خدمة المصادقة
  Future<void> initialize() async {
    // التحقق من وجود مستخدم محفوظ محلياً
    final userData = await _storageService.getUserData();
    if (userData != null) {
      _currentUser = userData;
    }
  }

  /// تسجيل الدخول
  Future<AuthResult> login(String email, String password) async {
    try {
      // التحقق من صحة البيانات
      if (email.isEmpty || password.isEmpty) {
        return AuthResult.failure('يرجى إدخال البريد الإلكتروني وكلمة المرور');
      }

      // البحث عن المستخدم في قاعدة البيانات
      final user = await _databaseService.getUserByEmail(email);
      if (user == null) {
        return AuthResult.failure('البريد الإلكتروني غير مسجل');
      }

      // التحقق من كلمة المرور
      final hashedPassword = _hashPassword(password);
      if (user.toJson()['password_hash'] != hashedPassword) {
        return AuthResult.failure('كلمة المرور غير صحيحة');
      }

      // التحقق من حالة المستخدم
      if (!user.isActive) {
        return AuthResult.failure('الحساب غير مفعل');
      }

      // حفظ بيانات المستخدم
      _currentUser = user;
      await _storageService.saveUserData(user);

      return AuthResult.success(user);
    } catch (e) {
      print('خطأ في تسجيل الدخول: $e');
      return AuthResult.failure('حدث خطأ أثناء تسجيل الدخول');
    }
  }

  /// تسجيل الخروج
  Future<void> logout() async {
    try {
      _currentUser = null;
      await _storageService.clearUserData();
    } catch (e) {
      print('خطأ في تسجيل الخروج: $e');
    }
  }

  /// تسجيل مستخدم جديد (للمديرين فقط)
  Future<AuthResult> registerUser({
    required String email,
    required String password,
    required String fullName,
    required UserType userType,
    String? phone,
    String? parentId,
  }) async {
    try {
      // التحقق من الصلاحيات
      if (!canCreateUser(userType)) {
        return AuthResult.failure(
            'ليس لديك صلاحية لإنشاء هذا النوع من المستخدمين');
      }

      // التحقق من عدم وجود المستخدم مسبقاً
      final existingUser = await _databaseService.getUserByEmail(email);
      if (existingUser != null) {
        return AuthResult.failure('البريد الإلكتروني مسجل مسبقاً');
      }

      // إنشاء المستخدم الجديد
      final newUser = User(
        id: _generateId(),
        email: email,
        fullName: fullName,
        phone: phone,
        userType: userType,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        parentId: parentId ?? _currentUser?.id,
      );

      // حفظ المستخدم في قاعدة البيانات مع كلمة المرور المشفرة
      final userJson = newUser.toJson();
      userJson['password_hash'] = _hashPassword(password);

      final createdUser = await _databaseService.createUser(
        User.fromJson(userJson),
      );

      if (createdUser != null) {
        return AuthResult.success(createdUser);
      } else {
        return AuthResult.failure('فشل في إنشاء المستخدم');
      }
    } catch (e) {
      print('خطأ في تسجيل المستخدم: $e');
      return AuthResult.failure('حدث خطأ أثناء تسجيل المستخدم');
    }
  }

  /// تغيير كلمة المرور
  Future<AuthResult> changePassword(
      String oldPassword, String newPassword) async {
    try {
      if (_currentUser == null) {
        return AuthResult.failure('يجب تسجيل الدخول أولاً');
      }

      // التحقق من كلمة المرور القديمة
      final hashedOldPassword = _hashPassword(oldPassword);
      final userData = await _databaseService.getUserById(_currentUser!.id);
      if (userData?.toJson()['password_hash'] != hashedOldPassword) {
        return AuthResult.failure('كلمة المرور القديمة غير صحيحة');
      }

      // تحديث كلمة المرور
      final hashedNewPassword = _hashPassword(newPassword);
      final updatedUserJson = _currentUser!.toJson();
      updatedUserJson['password_hash'] = hashedNewPassword;
      updatedUserJson['updated_at'] = DateTime.now().toIso8601String();

      final updatedUser = await _databaseService.updateUser(
        User.fromJson(updatedUserJson),
      );

      if (updatedUser != null) {
        _currentUser = updatedUser;
        await _storageService.saveUserData(updatedUser);
        return AuthResult.success(updatedUser);
      } else {
        return AuthResult.failure('فشل في تحديث كلمة المرور');
      }
    } catch (e) {
      print('خطأ في تغيير كلمة المرور: $e');
      return AuthResult.failure('حدث خطأ أثناء تغيير كلمة المرور');
    }
  }

  /// التحقق من إمكانية إنشاء نوع مستخدم معين
  bool canCreateUser(UserType userType) {
    if (_currentUser == null) return false;
    return _currentUser!.userType.canManage(userType);
  }

  /// التحقق من إمكانية إدارة مستخدم معين
  bool canManageUser(User user) {
    if (_currentUser == null) return false;
    return _currentUser!.canManage(user);
  }

  /// الحصول على الأنواع التي يمكن للمستخدم الحالي إدارتها
  List<UserType> getManagedUserTypes() {
    if (_currentUser == null) return [];
    return _currentUser!.userType.getManagedTypes();
  }

  /// تشفير كلمة المرور
  String _hashPassword(String password) {
    final bytes = utf8.encode(password);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// توليد معرف فريد
  String _generateId() {
    return DateTime.now().millisecondsSinceEpoch.toString();
  }
}

/// نتيجة عملية المصادقة
class AuthResult {
  final bool isSuccess;
  final String? message;
  final User? user;

  AuthResult._(this.isSuccess, this.message, this.user);

  factory AuthResult.success(User user) {
    return AuthResult._(true, null, user);
  }

  factory AuthResult.failure(String message) {
    return AuthResult._(false, message, null);
  }
}
