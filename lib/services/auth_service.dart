import 'package:crypto/crypto.dart';
import 'dart:convert';
import '../models/models.dart' as models;
import 'database_service.dart';
import 'storage_service.dart';

/// خدمة المصادقة والتفويض المبسطة للتطوير
class AuthService {
  static AuthService? _instance;
  static AuthService get instance => _instance ??= AuthService._();

  AuthService._();

  /// خدمة قاعدة البيانات
  final DatabaseService _databaseService = DatabaseService.instance;

  /// خدمة التخزين المحلي
  final StorageService _storageService = StorageService.instance;

  /// المستخدم الحالي
  models.User? _currentUser;
  models.User? get currentUser => _currentUser;

  /// التحقق من حالة تسجيل الدخول
  bool get isLoggedIn => _currentUser != null;

  /// تهيئة خدمة المصادقة
  Future<void> initialize() async {
    // التحقق من وجود مستخدم محفوظ محلياً
    final userData = await _storageService.getUserData();
    if (userData != null) {
      _currentUser = userData;
    }
  }

  /// تسجيل الدخول
  Future<AuthResult> login(String email, String password) async {
    try {
      // التحقق من صحة البيانات
      if (email.isEmpty || password.isEmpty) {
        return AuthResult.failure('يرجى إدخال البريد الإلكتروني وكلمة المرور');
      }

      // البحث عن المستخدم في قاعدة البيانات
      final user = await _databaseService.getUserByEmail(email);
      if (user == null) {
        return AuthResult.failure('البريد الإلكتروني غير مسجل');
      }

      // للتطوير: قبول أي كلمة مرور للمسؤول الوطني
      if (user.userType == models.UserType.nationalAdmin) {
        // حفظ بيانات المستخدم
        _currentUser = user;
        await _storageService.saveUserData(user);
        return AuthResult.success(user);
      }

      // للتطوير: قبول كلمة مرور "123456" لجميع المستخدمين
      if (password == "123456") {
        // التحقق من حالة المستخدم
        if (!user.isActive) {
          return AuthResult.failure('الحساب غير مفعل');
        }

        // حفظ بيانات المستخدم
        _currentUser = user;
        await _storageService.saveUserData(user);

        return AuthResult.success(user);
      } else {
        return AuthResult.failure('كلمة المرور غير صحيحة');
      }
    } catch (e) {
      return AuthResult.failure('حدث خطأ أثناء تسجيل الدخول');
    }
  }

  /// تسجيل الخروج
  Future<void> logout() async {
    try {
      _currentUser = null;
      await _storageService.clearUserData();
    } catch (e) {
      // تجاهل الأخطاء في تسجيل الخروج
    }
  }

  /// التحقق من إمكانية إنشاء نوع مستخدم معين
  bool canCreateUser(models.UserType userType) {
    if (_currentUser == null) return false;
    return _currentUser!.userType.canManage(userType);
  }

  /// التحقق من إمكانية إدارة مستخدم معين
  bool canManageUser(models.User user) {
    if (_currentUser == null) return false;
    return _currentUser!.canManage(user);
  }

  /// الحصول على الأنواع التي يمكن للمستخدم الحالي إدارتها
  List<models.UserType> getManagedUserTypes() {
    if (_currentUser == null) return [];
    return _currentUser!.userType.getManagedTypes();
  }

  /// تشفير كلمة المرور
  String _hashPassword(String password) {
    final bytes = utf8.encode(password);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// توليد معرف فريد
  String _generateId() {
    return DateTime.now().millisecondsSinceEpoch.toString();
  }
}

/// نتيجة عملية المصادقة
class AuthResult {
  final bool isSuccess;
  final String? message;
  final models.User? user;

  AuthResult._(this.isSuccess, this.message, this.user);

  factory AuthResult.success(models.User user) {
    return AuthResult._(true, null, user);
  }

  factory AuthResult.failure(String message) {
    return AuthResult._(false, message, null);
  }
}
