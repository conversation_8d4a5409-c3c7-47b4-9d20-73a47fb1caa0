import 'package:flutter/material.dart';
import '../../config/theme.dart';
import '../../config/app_config.dart';
import '../../config/routes.dart';
import '../../services/services.dart';
import '../../models/models.dart';

/// لوحة تحكم المسؤول الوطني
class NationalDashboard extends StatefulWidget {
  const NationalDashboard({super.key});

  @override
  State<NationalDashboard> createState() => _NationalDashboardState();
}

class _NationalDashboardState extends State<NationalDashboard> {
  final AuthService _authService = AuthService.instance;
  final DatabaseService _databaseService = DatabaseService.instance;

  User? _currentUser;
  List<Region> _regions = [];
  List<User> _regionalDirectors = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      _currentUser = _authService.currentUser;

      // تحميل الجهات
      _regions = await _databaseService.getAllRegions();

      // تحميل مدراء الأكاديميات الجهوية
      _regionalDirectors =
          await _databaseService.getUsersByType(UserType.regionalDirector);
    } catch (e) {
      print('خطأ في تحميل البيانات: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// تسجيل الخروج
  Future<void> _logout() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد تسجيل الخروج'),
        content: const Text('هل أنت متأكد من رغبتك في تسجيل الخروج؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('تسجيل الخروج'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await _authService.logout();
      if (mounted) {
        Navigator.of(context).pushReplacementNamed(AppRoutes.login);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('لوحة تحكم المسؤول الوطني'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'profile':
                  // TODO: فتح صفحة الملف الشخصي
                  break;
                case 'settings':
                  // TODO: فتح صفحة الإعدادات
                  break;
                case 'logout':
                  _logout();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'profile',
                child: ListTile(
                  leading: Icon(Icons.person),
                  title: Text('الملف الشخصي'),
                ),
              ),
              const PopupMenuItem(
                value: 'settings',
                child: ListTile(
                  leading: Icon(Icons.settings),
                  title: Text('الإعدادات'),
                ),
              ),
              const PopupMenuDivider(),
              const PopupMenuItem(
                value: 'logout',
                child: ListTile(
                  leading: Icon(Icons.logout, color: AppTheme.errorColor),
                  title: Text('تسجيل الخروج',
                      style: TextStyle(color: AppTheme.errorColor)),
                ),
              ),
            ],
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadData,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(AppConfig.paddingMedium),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // ترحيب بالمستخدم
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(AppConfig.paddingMedium),
                        child: Row(
                          children: [
                            CircleAvatar(
                              radius: 30,
                              backgroundColor: AppTheme.primaryColor,
                              child: Text(
                                _currentUser?.initials ?? 'م',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: AppConfig.fontSizeLarge,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            const SizedBox(width: AppConfig.paddingMedium),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'مرحباً، ${_currentUser?.shortName ?? 'المسؤول'}',
                                    style: Theme.of(context)
                                        .textTheme
                                        .headlineSmall,
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    _currentUser?.userType.displayNameAr ?? '',
                                    style: Theme.of(context)
                                        .textTheme
                                        .bodyMedium
                                        ?.copyWith(
                                          color: AppTheme.textSecondaryColor,
                                        ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: AppConfig.paddingLarge),

                    // إحصائيات سريعة
                    Text(
                      'إحصائيات عامة',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: AppConfig.paddingMedium),

                    Row(
                      children: [
                        Expanded(
                          child: _buildStatCard(
                            'الجهات',
                            _regions.length.toString(),
                            Icons.location_on,
                            AppTheme.primaryColor,
                          ),
                        ),
                        const SizedBox(width: AppConfig.paddingMedium),
                        Expanded(
                          child: _buildStatCard(
                            'مدراء الجهات',
                            _regionalDirectors.length.toString(),
                            Icons.people,
                            AppTheme.secondaryColor,
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: AppConfig.paddingLarge),

                    // الإجراءات السريعة
                    Text(
                      'الإجراءات السريعة',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: AppConfig.paddingMedium),

                    GridView.count(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      crossAxisCount: 2,
                      crossAxisSpacing: AppConfig.paddingMedium,
                      mainAxisSpacing: AppConfig.paddingMedium,
                      children: [
                        _buildActionCard(
                          'إدارة الجهات',
                          Icons.location_city,
                          () {
                            // TODO: فتح صفحة إدارة الجهات
                          },
                        ),
                        _buildActionCard(
                          'إدارة المستخدمين',
                          Icons.people_alt,
                          () {
                            // TODO: فتح صفحة إدارة المستخدمين
                          },
                        ),
                        _buildActionCard(
                          'الرسائل',
                          Icons.message,
                          () {
                            // TODO: فتح صفحة الرسائل
                          },
                        ),
                        _buildActionCard(
                          'التقارير',
                          Icons.analytics,
                          () {
                            // TODO: فتح صفحة التقارير
                          },
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
    );
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard(
      String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConfig.paddingMedium),
        child: Column(
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: AppConfig.paddingSmall),
            Text(
              value,
              style: TextStyle(
                fontSize: AppConfig.fontSizeXLarge,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة إجراء
  Widget _buildActionCard(String title, IconData icon, VoidCallback onTap) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppConfig.borderRadius),
        child: Padding(
          padding: const EdgeInsets.all(AppConfig.paddingMedium),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, size: 40, color: AppTheme.primaryColor),
              const SizedBox(height: AppConfig.paddingSmall),
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
